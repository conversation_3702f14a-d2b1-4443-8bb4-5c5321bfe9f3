import { StructuredResumeData } from './resume-structured';

// Template data format that matches the Handlebars placeholders in templates
export interface ResumeTemplateData {
  // Personal info - mapped to header placeholders
  name: string;
  email: string;
  phone?: string;
  linkedin?: string;
  location: string;
  website?: string;
  github?: string;

  // Professional summary and job title
  summary: string;
  jobTitle: string;

  // Work experience - mapped to {{#each experiences}}
  experiences: Array<{
    id: string;
    title: string;        // job title
    jobTitle: string;     // alternative field name used in some templates
    company: string;
    location: string;
    startDate: string;
    endDate: string;
    responsibilities: string[];
  }>;

  // Education - mapped to {{#each education}}
  education: Array<{
    id: string;
    degree: string;
    institution: string;
    school: string;       // alternative field name used in some templates
    location?: string;
    graduationDate: string;
    startYear?: string;   // extracted from graduationDate for some templates
    endYear?: string;     // extracted from graduationDate for some templates
    gpa?: string;
    relevantCoursework?: string[];
    honors?: string[];
  }>;

  // Skills - mapped to {{#each skills}} or {{#each skillCategories}}
  skills: Array<{
    category: string;
    skills: string;       // joined string of skills for template display
  }>;
  skillCategories: Array<{
    category: string;
    skills: string;       // joined string of skills for template display
  }>;

  // Projects - mapped to {{#each projects}}
  projects?: Array<{
    id: string;
    title: string;
    description: string;
    technologies: string; // joined string of technologies
    link?: string;
    achievements?: string[];
  }>;

  // Certifications - mapped to {{#each certifications}}
  certifications?: Array<{
    id: string;
    name: string;
    issuer: string;
    date: string;
    year: string;         // extracted year for some templates
    credentialId?: string;
  }>;

  // Languages - mapped to {{#each languages}}
  languages?: Array<{
    language: string;
    proficiency: string;
  }>;

  // Awards - mapped to {{#each awards}}
  awards?: Array<{
    id: string;
    title: string;
    issuer: string;
    date: string;
    description?: string;
  }>;
}

/**
 * Convert structured resume data to template-compatible format
 * Maps the structured data to the placeholder format used in Handlebars templates
 */
export function convertToTemplateData(data: StructuredResumeData): ResumeTemplateData {
  // Helper function to extract year from date string
  const extractYear = (dateString: string): string => {
    if (!dateString) return '';
    const year = new Date(dateString).getFullYear();
    return isNaN(year) ? dateString : year.toString();
  };

  // Helper function to extract start/end years from graduation date
  const extractEducationYears = (graduationDate: string): { startYear: string; endYear: string } => {
    const endYear = extractYear(graduationDate);
    const startYearNum = parseInt(endYear) - 4; // Assume 4-year degree
    return {
      startYear: startYearNum > 0 ? startYearNum.toString() : '',
      endYear
    };
  };

  return {
    // Personal information
    name: data.personalInfo.fullName,
    email: data.personalInfo.email,
    phone: data.personalInfo.phone,
    linkedin: data.personalInfo.linkedin,
    location: data.personalInfo.location,
    website: data.personalInfo.website,
    github: data.personalInfo.github,

    // Professional summary and target position
    summary: data.professionalSummary,
    jobTitle: data.targetPosition,

    // Work experience
    experiences: Array.isArray(data.experiences) ? data.experiences.map(exp => ({
      id: exp.id,
      title: exp.jobTitle,
      jobTitle: exp.jobTitle, // duplicate for template compatibility
      company: exp.company,
      location: exp.location,
      startDate: exp.startDate,
      endDate: exp.endDate,
      responsibilities: Array.isArray(exp.responsibilities) ? exp.responsibilities : [],
    })) : [],

    // Education
    education: Array.isArray(data.education) ? data.education.map(edu => {
      const years = extractEducationYears(edu.graduationDate);
      return {
        id: edu.id,
        degree: edu.degree,
        institution: edu.institution,
        school: edu.institution, // duplicate for template compatibility
        location: edu.location,
        graduationDate: edu.graduationDate,
        startYear: years.startYear,
        endYear: years.endYear,
        gpa: edu.gpa,
        relevantCoursework: edu.relevantCoursework,
        honors: edu.honors,
      };
    }) : [],

    // Skills - create both formats for template compatibility
    skills: data.skills?.categories?.map(cat => ({
      category: cat.category,
      skills: Array.isArray(cat.skills) ? cat.skills.join(', ') : '',
    })) || [],
    skillCategories: data.skills?.categories?.map(cat => ({
      category: cat.category,
      skills: Array.isArray(cat.skills) ? cat.skills.join(', ') : '',
    })) || [],

    // Projects
    projects: data.projects?.map(proj => ({
      id: proj.id,
      title: proj.title,
      description: proj.description,
      technologies: Array.isArray(proj.technologies) ? proj.technologies.join(', ') : '',
      link: proj.link,
      achievements: proj.achievements,
    })),

    // Certifications
    certifications: data.certifications?.map(cert => ({
      id: cert.id,
      name: cert.name,
      issuer: cert.issuer,
      date: cert.date,
      year: extractYear(cert.date),
      credentialId: cert.credentialId,
    })),

    // Languages
    languages: data.languages,

    // Awards
    awards: data.awards,
  };
}

/**
 * Validate that required fields are present for template rendering
 */
export function validateTemplateData(data: ResumeTemplateData): { isValid: boolean; missingFields: string[] } {
  const requiredFields = ['name', 'email', 'jobTitle'];
  const missingFields: string[] = [];

  for (const field of requiredFields) {
    if (!data[field as keyof ResumeTemplateData]) {
      missingFields.push(field);
    }
  }

  return {
    isValid: missingFields.length === 0,
    missingFields
  };
}